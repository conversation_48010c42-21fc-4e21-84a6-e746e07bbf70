{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 4, "useTabs": false, "bracketSpacing": true, "arrowParens": "always", "endOfLine": "lf", "jsxSingleQuote": false, "bracketSameLine": false, "quoteProps": "as-needed", "overrides": [{"files": "*.{js,jsx,ts,tsx}", "options": {"tabWidth": 4, "singleQuote": true, "semi": true}}, {"files": "*.{json,jsonc}", "options": {"tabWidth": 2, "singleQuote": false}}, {"files": "*.md", "options": {"tabWidth": 2, "printWidth": 80, "proseWrap": "always"}}]}