{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "ts-node src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.15.0", "dependencies": {"@fastify/cors": "^11.1.0", "@trpc/server": "^11.5.0", "ajv": "latest", "fastify": "^5.5.0", "superjson": "^2.2.2", "zod": "^4.1.3"}, "devDependencies": {"@types/node": "^24.3.0", "ts-node": "^10.9.2", "typescript": "~5.9.2"}}