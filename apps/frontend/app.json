{"expo": {"name": "frontend", "slug": "frontend", "version": "1.0.0", "scheme": "codechallengeidilio", "entryPoint": "./App.tsx", "web": {"favicon": "./assets/favicon.png"}, "experiments": {"tsconfigPaths": true}, "plugins": ["expo-router"], "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.codechallengeido.frontend"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.codechallengeido.frontend"}}}