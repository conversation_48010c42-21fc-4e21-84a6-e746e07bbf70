import { Badge, Box, Heading, Text } from '@/components/ui';
import { useShowByID } from '@/hooks/use-shows';
import { Category } from '@prisma/client';
import { useLocalSearchParams, useRouter } from 'expo-router';
import {
    ArrowLeft,
    Calendar,
    ChevronDown,
    Clock,
    Download,
    Heart,
    Play,
    Plus,
    Share,
    Star,
    Tv,
} from 'lucide-react-native';
import { useMemo, useState } from 'react';
import {
    Dimensions,
    FlatList,
    Image,
    Modal,
    ScrollView,
    StatusBar,
    TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { categoryLabels } from '../../../../packages/api/src/types/show';

const { width } = Dimensions.get('window');

export default function DetailScreen() {
    const { id } = useLocalSearchParams<{ id: string }>();
    const router = useRouter();

    const [isLiked, setIsLiked] = useState(false);
    const [isBookmarked, setIsBookmarked] = useState(false);
    const [selectedSeason, setSelectedSeason] = useState(1);
    const [showSeasonModal, setShowSeasonModal] = useState(false);
    const [userRating, setUserRating] = useState(0);
    const [showRatingModal, setShowRatingModal] = useState(false);

    const { data: show, isLoading, error } = useShowByID(id);

    // Agrupar episodios por temporada
    const episodesBySeason = useMemo(() => {
        if (!show?.episodes) return {};

        return show.episodes.reduce((acc: Record<number, typeof show.episodes>, episode) => {
            const season = episode.num_season;
            if (!acc[season]) {
                acc[season] = [];
            }
            acc[season].push(episode);
            return acc;
        }, {});
    }, [show]);

    const seasons = Object.keys(episodesBySeason)
        .map(Number)
        .sort((a, b) => a - b);
    const currentSeasonEpisodes = episodesBySeason[selectedSeason] || [];

    const handleLikePress = () => {
        setIsLiked(!isLiked);
    };

    const handleBookmarkPress = () => {
        setIsBookmarked(!isBookmarked);
    };

    // Estados de error y loading
    if ((error || !show) && !isLoading) {
        return (
            <SafeAreaView className="flex-1 bg-slate-900">
                <StatusBar barStyle="light-content" backgroundColor="#0F172A" />
                <Box className="flex-1 justify-center items-center px-6">
                    <Box className="w-24 h-24 rounded-full bg-red-500/10 justify-center items-center mb-8 border-2 border-red-500/30">
                        <Text className="text-4xl">⚠️</Text>
                    </Box>
                    <Heading className="text-white text-3xl font-black text-center mb-4 tracking-tight">
                        ¡Oops! Algo salió mal
                    </Heading>
                    <Text className="text-white/60 text-lg text-center mb-10 leading-7 max-w-sm">
                        No pudimos cargar los detalles del contenido. Inténtalo de nuevo más tarde.
                    </Text>
                    <TouchableOpacity
                        className="flex-row items-center bg-gradient-to-r from-violet-600 to-purple-600 px-8 py-4 rounded-2xl shadow-2xl shadow-violet-500/40"
                        onPress={() => router.back()}
                        activeOpacity={0.9}
                    >
                        <ArrowLeft size={22} color="#FFFFFF" />
                        <Text className="text-white text-lg font-black ml-3 tracking-tight">
                            Volver al inicio
                        </Text>
                    </TouchableOpacity>
                </Box>
            </SafeAreaView>
        );
    }

    if (isLoading) {
        return (
            <SafeAreaView className="flex-1 bg-slate-900">
                <StatusBar barStyle="light-content" backgroundColor="#0F172A" />
                <Box className="flex-1 justify-center items-center">
                    <Box className="w-16 h-16 rounded-full bg-violet-500/20 mb-6 animate-pulse" />
                    <Heading className="text-white text-2xl font-black mb-2">Cargando...</Heading>
                    <Text className="text-white/60 text-base">Preparando el contenido</Text>
                </Box>
            </SafeAreaView>
        );
    }

    // Componente para el selector de temporadas
    const SeasonSelector = () => (
        <TouchableOpacity
            className="flex-row items-center bg-white/8 border border-white/15 px-4 py-3 rounded-xl"
            onPress={() => setShowSeasonModal(true)}
            activeOpacity={0.8}
        >
            <Calendar size={16} color="#8B5CF6" />
            <Text className="text-white font-bold text-sm ml-2 mr-1">
                Temporada {selectedSeason}
            </Text>
            <ChevronDown size={16} color="#FFFFFF" />
        </TouchableOpacity>
    );

    // Modal selector de temporadas
    const SeasonModal = () => (
        <Modal
            visible={showSeasonModal}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setShowSeasonModal(false)}
        >
            <TouchableOpacity
                className="flex-1 bg-black/60 justify-center items-center px-6"
                activeOpacity={1}
                onPress={() => setShowSeasonModal(false)}
            >
                <Box className="bg-slate-800 rounded-3xl p-6 w-full max-w-sm border border-white/10">
                    <Heading className="text-white text-xl font-bold text-center mb-6">
                        Seleccionar Temporada
                    </Heading>
                    <ScrollView showsVerticalScrollIndicator={false} className="max-h-64">
                        {seasons.map((season) => (
                            <TouchableOpacity
                                key={season}
                                className={`flex-row items-center justify-between p-4 rounded-xl mb-2 ${
                                    selectedSeason === season
                                        ? 'bg-violet-500/20 border border-violet-500/40'
                                        : 'bg-white/5 border border-white/10'
                                }`}
                                onPress={() => {
                                    setSelectedSeason(season);
                                    setShowSeasonModal(false);
                                }}
                                activeOpacity={0.8}
                            >
                                <Box className="flex-row items-center">
                                    <Calendar
                                        size={20}
                                        color={selectedSeason === season ? '#8B5CF6' : '#FFFFFF'}
                                    />
                                    <Text
                                        className={`font-bold text-lg ml-3 ${
                                            selectedSeason === season
                                                ? 'text-violet-400'
                                                : 'text-white'
                                        }`}
                                    >
                                        Temporada {season}
                                    </Text>
                                </Box>
                                <Text className="text-white/60 text-sm font-medium">
                                    {episodesBySeason[season]?.length} episodios
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </ScrollView>
                </Box>
            </TouchableOpacity>
        </Modal>
    );

    // Componente de rating con estrellas
    const StarRating = ({
        rating,
        onRatingChange,
    }: {
        rating: number;
        onRatingChange: (rating: number) => void;
    }) => {
        const stars = [];

        for (let i = 1; i <= 5; i++) {
            const isFullStar = rating >= i;
            const isHalfStar = rating >= i - 0.5 && rating < i;

            stars.push(
                <TouchableOpacity
                    key={i}
                    onPress={() => onRatingChange(i)}
                    onLongPress={() => onRatingChange(i - 0.5)}
                    activeOpacity={0.7}
                    className="mx-1"
                >
                    <Box className="relative">
                        {isHalfStar ? (
                            <Box className="flex-row">
                                <Box className="overflow-hidden" style={{ width: 12 }}>
                                    <Star size={24} color="#FFD700" fill="#FFD700" />
                                </Box>
                                <Box style={{ marginLeft: -12, width: 12 }}>
                                    <Star size={24} color="#4B5563" fill="transparent" />
                                </Box>
                            </Box>
                        ) : (
                            <Star
                                size={24}
                                color={isFullStar ? '#FFD700' : '#4B5563'}
                                fill={isFullStar ? '#FFD700' : 'transparent'}
                            />
                        )}
                    </Box>
                </TouchableOpacity>
            );
        }

        return <Box className="flex-row items-center justify-center">{stars}</Box>;
    };

    // Modal de calificación
    const RatingModal = () => (
        <Modal
            visible={showRatingModal}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setShowRatingModal(false)}
        >
            <TouchableOpacity
                className="flex-1 bg-black/70 justify-center items-center px-6"
                activeOpacity={1}
                onPress={() => setShowRatingModal(false)}
            >
                <TouchableOpacity
                    className="bg-slate-800 rounded-3xl p-8 w-full max-w-sm border border-white/10 relative"
                    activeOpacity={1}
                    onPress={(e) => e.stopPropagation()}
                >
                    {/* Botón cerrar */}
                    <TouchableOpacity
                        className="absolute top-4 right-4 w-8 h-8 rounded-full bg-white/10 justify-center items-center"
                        onPress={() => setShowRatingModal(false)}
                        activeOpacity={0.7}
                    >
                        <Text className="text-white text-lg font-bold">×</Text>
                    </TouchableOpacity>

                    <Heading className="text-white text-2xl font-bold text-center mb-2">
                        Calificar Serie
                    </Heading>
                    <Text className="text-white/70 text-center mb-6">
                        Toca para calificar de 1 a 5, mantén presionado para media estrella
                    </Text>

                    <StarRating
                        rating={userRating}
                        onRatingChange={(rating) => {
                            setUserRating(rating);
                            // Cerrar automáticamente después de un pequeño delay
                            setTimeout(() => {
                                setShowRatingModal(false);
                            }, 300);
                        }}
                    />

                    <Text className="text-white text-center text-lg font-semibold mt-4">
                        {userRating > 0
                            ? `${userRating}/5 estrellas`
                            : 'Selecciona tu calificación'}
                    </Text>
                </TouchableOpacity>
            </TouchableOpacity>
        </Modal>
    );

    const renderEpisode = ({
        item,
        index,
    }: {
        item: NonNullable<typeof show>['episodes'][0];
        index: number;
    }) => (
        <TouchableOpacity
            className="bg-white/3 rounded-3xl p-5 mb-4 border-2 border-violet-500/30 shadow-xl shadow-violet-500/20 hover:border-violet-500/50 transition-colors"
            activeOpacity={0.9}
        >
            <Box className="flex-row items-center mb-4">
                <Box className="w-12 h-12 rounded-xl bg-yellow-600/80 justify-center items-center mr-4 shadow-lg shadow-yellow-600/30 border border-yellow-500/40">
                    <Text className="text-white text-lg font-black">{item.num_episode}</Text>
                </Box>
                <Box className="flex-1">
                    <Text
                        className="text-white text-lg font-black mb-1 leading-6"
                        numberOfLines={1}
                    >
                        {item.name}
                    </Text>
                    <Box className="flex-row items-center gap-4">
                        <Box className="flex-row items-center">
                            <Clock size={12} color="#8B5CF6" />
                            <Text className="text-violet-400 text-xs font-semibold ml-1">
                                {item.duration} min
                            </Text>
                        </Box>
                        <Box className="flex-row items-center">
                            <Star size={12} color="#FFD700" fill="#FFD700" />
                            <Text className="text-yellow-400 text-xs font-semibold ml-1">
                                {item.score || 0}
                            </Text>
                        </Box>
                    </Box>
                </Box>
                <TouchableOpacity
                    className="w-12 h-12 rounded-2xl bg-white/10 border border-white/20 justify-center items-center"
                    activeOpacity={0.8}
                >
                    <Play size={18} color="#FFFFFF" fill="#FFFFFF" />
                </TouchableOpacity>
            </Box>

            <Text className="text-white/70 text-sm leading-6 mb-3" numberOfLines={3}>
                {item.description}
            </Text>

            <Box className="flex-row justify-between items-center">
                <Badge
                    className={`${index < 3 ? 'bg-green-500/20 border-green-500/40' : 'bg-blue-500/20 border-blue-500/40'}`}
                >
                    <Text
                        className={`text-xs font-bold ${index < 3 ? 'text-green-400' : 'text-blue-400'}`}
                    >
                        {index < 3 ? '✓ Visto' : '• Nuevo'}
                    </Text>
                </Badge>
                <TouchableOpacity className="flex-row items-center">
                    <Download size={14} color="#8B5CF6" />
                    <Text className="text-violet-400 text-xs font-bold ml-1">Descargar</Text>
                </TouchableOpacity>
            </Box>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView className="flex-1 bg-slate-900">
            <StatusBar barStyle="light-content" backgroundColor="#0F172A" />

            <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
                {/* Hero Section Mejorado */}
                <Box className="relative">
                    <Image
                        source={{ uri: show?.showImages?.[0]?.url }}
                        style={{ width: width, height: 420 }}
                        className="bg-slate-800"
                        resizeMode="cover"
                    />

                    {/* Gradiente overlay mejorado */}
                    <Box className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/50 to-black/90" />

                    {/* Header con botones flotantes */}
                    <Box className="absolute top-12 left-0 right-0 flex-row justify-between items-start px-6">
                        <TouchableOpacity
                            className="w-12 h-12 rounded-2xl bg-black/60 border border-white/20 justify-center items-center backdrop-blur-xl"
                            onPress={() => router.back()}
                            activeOpacity={0.8}
                        >
                            <ArrowLeft size={20} color="#FFFFFF" />
                        </TouchableOpacity>

                        <Box className="flex-row gap-3">
                            <TouchableOpacity
                                className="w-12 h-12 rounded-2xl bg-black/60 border border-white/20 justify-center items-center backdrop-blur-xl"
                                onPress={handleBookmarkPress}
                                activeOpacity={0.8}
                            >
                                <Plus size={18} color={isBookmarked ? '#8B5CF6' : '#FFFFFF'} />
                            </TouchableOpacity>
                            <TouchableOpacity
                                className={`w-12 h-12 rounded-2xl border justify-center items-center backdrop-blur-xl ${
                                    isLiked
                                        ? 'bg-red-500/20 border-red-500/50'
                                        : 'bg-black/60 border-white/20'
                                }`}
                                onPress={handleLikePress}
                                activeOpacity={0.8}
                            >
                                <Heart
                                    size={18}
                                    color={isLiked ? '#EF4444' : '#FFFFFF'}
                                    fill={isLiked ? '#EF4444' : 'none'}
                                />
                            </TouchableOpacity>
                        </Box>
                    </Box>

                    {/* Información del show en hero */}
                    <Box className="absolute bottom-0 left-0 right-0 p-6">
                        <Heading className="text-white text-4xl font-black mb-3 leading-tight tracking-tight">
                            {show?.name}
                        </Heading>
                        <Box className="flex-row items-center mb-4">
                            <Box className="flex-row items-center bg-black/40 backdrop-blur-xl px-3 py-2 rounded-xl mr-3">
                                <Star size={14} color="#FFD700" fill="#FFD700" />
                                <Text className="text-white font-bold text-sm ml-1">
                                    {show?.score}
                                </Text>
                            </Box>
                            <Box className="bg-black/40 backdrop-blur-xl px-3 py-2 rounded-xl mr-3">
                                <Text className="text-white font-bold text-sm">
                                    {show?.year_released}
                                </Text>
                            </Box>
                            <Box className="bg-black/40 backdrop-blur-xl px-3 py-2 rounded-xl mr-3">
                                <Text className="text-white font-bold text-sm">HD</Text>
                            </Box>
                            {userRating > 0 && (
                                <Box className="flex-row items-center bg-yellow-500/20 backdrop-blur-xl px-3 py-2 rounded-xl border border-yellow-500/40">
                                    <Star size={12} color="#FFD700" fill="#FFD700" />
                                    <Text className="text-yellow-400 font-bold text-sm ml-1">
                                        Mi {userRating}
                                    </Text>
                                </Box>
                            )}
                        </Box>
                    </Box>
                </Box>

                {/* Contenido principal */}
                <Box className="px-6 py-8">
                    {/* Botones de acción principales */}
                    <Box className="flex-row mb-8">
                        <TouchableOpacity
                            className="flex-1 flex-row items-center justify-center bg-white py-4 rounded-2xl mr-4 shadow-xl shadow-white/20"
                            activeOpacity={0.9}
                        >
                            <Play size={22} color="#000000" fill="#000000" />
                            <Text className="text-black text-lg font-black ml-3 tracking-tight">
                                Reproducir
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            className="w-14 h-14 rounded-2xl bg-white/10 border border-white/20 justify-center items-center mr-3"
                            activeOpacity={0.8}
                        >
                            <Download size={24} color="#FFFFFF" />
                        </TouchableOpacity>

                        <TouchableOpacity
                            className="w-14 h-14 rounded-2xl bg-white/10 border border-white/20 justify-center items-center mr-3"
                            activeOpacity={0.8}
                        >
                            <Share size={24} color="#FFFFFF" />
                        </TouchableOpacity>

                        <TouchableOpacity
                            className={`w-14 h-14 rounded-2xl border justify-center items-center ${
                                userRating > 0
                                    ? 'bg-yellow-500/20 border-yellow-500/50'
                                    : 'bg-white/10 border-white/20'
                            }`}
                            onPress={() => setShowRatingModal(true)}
                            activeOpacity={0.8}
                        >
                            <Star
                                size={24}
                                color={userRating > 0 ? '#FFD700' : '#FFFFFF'}
                                fill={userRating > 0 ? '#FFD700' : 'none'}
                            />
                        </TouchableOpacity>
                    </Box>

                    {/* Estadísticas mejoradas */}
                    <Box className="bg-white/5 rounded-3xl p-6 mb-8 border border-white/10">
                        <Box className="flex-row justify-between">
                            <Box className="flex-1 items-center">
                                <Box className="flex-row items-center mb-1">
                                    <Play size={16} color="#8B5CF6" />
                                    <Text className="text-white text-xl font-black ml-1">
                                        {show?.episodes?.length || 12}
                                    </Text>
                                </Box>
                                <Text className="text-white/60 text-sm font-semibold">
                                    Episodios
                                </Text>
                            </Box>
                            <Box className="w-px h-12 bg-white/20" />
                            <Box className="flex-1 items-center">
                                <Box className="flex-row items-center mb-1">
                                    <Tv size={16} color="#10B981" />
                                    <Text className="text-white text-xl font-black ml-1">
                                        {seasons.length}
                                    </Text>
                                </Box>
                                <Text className="text-white/60 text-sm font-semibold">
                                    Temporadas
                                </Text>
                            </Box>
                            <Box className="w-px h-12 bg-white/20" />
                            <Box className="flex-1 items-center">
                                <Box className="flex-row items-center mb-1">
                                    <Heart size={16} color="#EC4899" />
                                    <Text className="text-white text-xl font-black ml-1">
                                        {isLiked ? '2.3K' : '2.2K'}
                                    </Text>
                                </Box>
                                <Text className="text-white/60 text-sm font-semibold">
                                    Me gusta
                                </Text>
                            </Box>
                            <Box className="w-px h-12 bg-white/20" />
                            <Box className="flex-1 items-center">
                                <Box className="flex-row items-center mb-1">
                                    <Star
                                        size={16}
                                        color="#FFD700"
                                        fill={userRating > 0 ? '#FFD700' : 'none'}
                                    />
                                    <Text className="text-white text-xl font-black ml-1">
                                        {userRating > 0 ? userRating.toFixed(1) : '0.0'}
                                    </Text>
                                </Box>
                                <Text className="text-white/60 text-sm font-semibold">
                                    Tu calificación
                                </Text>
                            </Box>
                        </Box>
                    </Box>

                    {/* Descripción */}
                    <Box className="mb-8">
                        <Heading className="text-white text-xl font-black mb-4 tracking-tight">
                            Sinopsis
                        </Heading>
                        <Text className="text-white/80 text-base leading-7 mb-4">
                            {show?.description}
                        </Text>
                        <Box className="flex-row flex-wrap gap-2">
                            <Badge className="bg-violet-500/20 border-violet-500/40">
                                <Text className="text-violet-400 text-xs font-bold">
                                    {categoryLabels[show?.category as Category]}
                                </Text>
                            </Badge>

                            <Badge className="bg-green-500/20 border-green-500/40">
                                <Text className="text-green-400 text-xs font-bold">
                                    {show?.year_released}
                                </Text>
                            </Badge>
                        </Box>
                    </Box>

                    {/* Sección de episodios con selector de temporadas */}
                    <Box className="mb-6">
                        <Box className="flex-row justify-between items-center mb-6">
                            <Heading className="text-white text-2xl font-black tracking-tight">
                                Episodios
                            </Heading>
                            <SeasonSelector />
                        </Box>

                        <FlatList
                            data={currentSeasonEpisodes}
                            renderItem={renderEpisode}
                            keyExtractor={(item) => item.id}
                            showsVerticalScrollIndicator={false}
                            scrollEnabled={false}
                        />
                    </Box>
                </Box>
            </ScrollView>

            <SeasonModal />
            <RatingModal />
        </SafeAreaView>
    );
}
