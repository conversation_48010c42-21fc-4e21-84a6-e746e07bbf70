import { useEffect, useState } from 'react';

interface UseAutoCarouselOptions {
    images: string[];
    interval?: number;
    enabled?: boolean;
}

export const useAutoCarousel = ({
    images,
    interval = 3000,
    enabled = true,
}: UseAutoCarouselOptions) => {
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        if (!enabled || images.length <= 1) {
            console.log('Auto-carousel disabled or not enough images:', {
                enabled,
                imageCount: images.length,
            });
            return;
        }

        console.log('Starting auto-carousel with interval:', interval);
        const timer = setInterval(() => {
            setCurrentIndex((prevIndex) => {
                const nextIndex = (prevIndex + 1) % images.length;
                console.log('Carousel changing image:', prevIndex, '->', nextIndex);
                return nextIndex;
            });
        }, interval);

        return () => {
            console.log('Cleaning up auto-carousel timer');
            clearInterval(timer);
        };
    }, [images.length, interval, enabled]);

    return {
        currentIndex,
        currentImage: images[currentIndex] || images[0],
        setCurrentIndex,
    };
};
