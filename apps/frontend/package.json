{"name": "frontend", "version": "1.0.0", "scripts": {"android": "expo run:android", "ios": "expo run:ios", "start": "expo start", "start:tunel": "expo start --tunnel", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@expo/html-elements": "^0.10.1", "@gluestack-style/react": "^1.0.57", "@gluestack-ui/core": "^3.0.2", "@gluestack-ui/themed": "^1.1.73", "@gluestack-ui/utils": "^3.0.2", "@legendapp/motion": "^2.4.0", "@tanstack/react-query": "^5.85.5", "@trpc/client": "^11.5.0", "@trpc/react-query": "^11.5.0", "@trpc/server": "^11.5.0", "@trpc/tanstack-react-query": "^11.5.0", "babel-plugin-module-resolver": "^5.0.2", "expo": "~53.0.22", "expo-constants": "~17.1.7", "expo-linking": "~7.1.7", "expo-router": "~5.1.5", "expo-status-bar": "~2.2.3", "lucide-react-native": "^0.542.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-aria": "^3.43.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "4.14.1", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.1", "react-native-web": "^0.20.0", "react-stately": "^3.41.0", "superjson": "^2.2.2", "tailwind-variants": "^0.1.20", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^4.4.4", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true, "main": "expo-router/entry"}