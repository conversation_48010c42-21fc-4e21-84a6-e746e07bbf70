{"name": "codcxhallengeidilio", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prisma:migrate": "pnpm --filter api exec prisma migrate dev --schema=./prisma/schema.prisma", "prisma:generate": "pnpm --filter api exec prisma generate --schema=./prisma/schema.prisma", "dev:back": "pnpm --filter backend run dev", "dev:front": "pnpm --filter frontend run start", "dev:front:tunel": "pnpm --filter frontend run start:tunel", "prepare": "husky", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "pnpm --filter frontend run lint", "lint:fix": "pnpm --filter frontend run format"}, "lint-staged": {"apps/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "packages/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.15.0", "type": "module", "devDependencies": {"husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "typescript-eslint": "^8.41.0"}}