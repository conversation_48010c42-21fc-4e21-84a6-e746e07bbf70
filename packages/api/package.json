{"name": "api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "seed": "ts-node scripts/seed.ts", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:reset": "prisma migrate reset --force && pnpm seed"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.15.0", "dependencies": {"@faker-js/faker": "^10.0.0", "@prisma/client": "^6.14.0", "@supabase/supabase-js": "^2.56.0", "@trpc/server": "^11.5.0", "uuid": "^11.1.0", "zod": "^4.1.3"}, "devDependencies": {"@types/node": "^24.3.0", "prisma": "^6.14.0", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}