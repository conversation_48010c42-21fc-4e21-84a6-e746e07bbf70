generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Category {
  ACTION
  COMEDY
  DRAMA
  FANTASY
  HORROR
  ROMANCE
  SCI_FI
}


model Show {
  id            String          @id @default(uuid())
  name          String
  description   String
  category      Category
  num_seasons   Int
  year_released Int

  showImages    ShowImage[]
  showRatings   ShowRating[]
  userLikeShows UserLikeShows[]
  episodes      Episode[]
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt

  @@index([name])
  @@index([category])
  @@index([year_released])
}

model Episode {
  id            String          @id @default(uuid())
  show_id       String
  name          String
  num_episode   Int
  num_season    Int
  description   String
  duration      Int
  show          Show            @relation(fields: [show_id], references: [id], onDelete: Cascade)
  episodeRatings EpisodeRating[]
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt

  @@index([show_id])
  @@index([num_season, num_episode])
}

model ShowImage {
  id        String   @id @default(uuid())
  show_id   String
  url       String
  show      Show     @relation(fields: [show_id], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@index([show_id])
}

model ShowRating {
  id        String   @id @default(uuid())
  user_id   String
  show_id   String
  score     Int
  review    String?
  show      Show     @relation(fields: [show_id], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@unique([user_id, show_id])
  @@index([show_id])
  @@index([user_id, show_id])
}

model EpisodeRating {
  id          String   @id @default(uuid())
  user_id     String
  episode_id  String
  score       Int
  review      String?
  episode     Episode  @relation(fields: [episode_id], references: [id], onDelete: Cascade)

  @@unique([user_id, episode_id])
  @@index([episode_id])
  @@index([user_id, episode_id])
}

model UserLikeShows {
  id        String   @id @default(uuid())
  user_id   String
  show_id   String
  show      Show     @relation(fields: [show_id], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@unique([user_id, show_id])
  @@index([show_id])
}